import React from "react";
import { Mo<PERSON>, Box, TextField, InputAdornment, MenuItem } from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input from "@/components/libs/Input";
import Image from "next/image";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";

const DEFAULT_INPUTS = {};

export default class TransactionModalVerify extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "",
      formData: null,
      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    };
  }

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/transaction/detail", {
      id: this.state.inputs.id,
    });

    let inputs = structuredClone(DEFAULT_INPUTS);
    if (response.status === 200) {
      inputs = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ inputs });
  };

  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };

  onLoading = (show, timout = 300) => {
    if (show) {
      this.ref_Loading.onShowDialog();
      return;
    } else {
      setTimeout(() => {
        this.ref_Loading.onCloseDialog();
      }, timout);
    }
  };

  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
      },
      () => {
        this.onFetchDetail();
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "",
      formData: null,
      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
      fetchData: {},
    });
  };

  onTextInputListeners = (text, input) => {
    let inputs = { ...this.state.inputs };
    inputs[input] = text;
    this.setState({ inputs });
  };

  onTextErrorListeners = (error, input) => {
    this.setState((prevState) => ({
      ...prevState,
      errors: { ...prevState.errors, [input]: error },
    }));
  };
  onValidateListeners = () => {
    if (this.state.formType === "verify") {
      this.actOnVerifyListeners();
    }
  };

  actOnVerifyListeners = async () => {
    this.ref_Loading.onShowDialog();

    let params = structuredClone(this.state.inputs);
    const response = await ApiHelper.post(
      "kiosk/admin/transaction/verify",
      params
    );

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Verifikasi", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  render() {
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className="modal-content">
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Verifikasi Transaksi</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    const { inputs } = this.state;
    if (!inputs) return null;

    return (
      <div className="form">
        <div className="form-group">
          <div className="form-row">
            <div className="form-col">
              <Input
                label="Nomor Transaksi"
                value={inputs.number}
                readonly={true}
              />
            </div>
            <div className="form-col">
              <Input
                label="Tanggal Input"
                value={inputs.input_datetime}
                readonly={true}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-col">
              <Input
                label="Nama Pelanggan"
                value={inputs.customer_name}
                readonly={true}
              />
            </div>
            <div className="form-col">
              <Input
                label="WhatsApp"
                value={inputs.customer_whatsapp}
                readonly={true}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-col">
              <Input
                label="Afiliasi"
                value={inputs.affiliate_name}
                readonly={true}
              />
            </div>
            <div className="form-col">
              <Input
                label="Kode Afiliasi"
                value={inputs.affiliate_code}
                readonly={true}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-col">
              <Input
                label="Total Item"
                value={inputs.total_product_item}
                readonly={true}
              />
            </div>
            <div className="form-col">
              <Input
                label="Total"
                value={new Intl.NumberFormat("id-ID", {
                  style: "currency",
                  currency: "IDR",
                }).format(inputs.grand_total)}
                readonly={true}
              />
            </div>
          </div>
        </div>

        <div className="form-group">
          <div className="form-title">Detail Produk</div>
          {inputs.product_array &&
            inputs.product_array.map((product, index) => (
              <div key={index} className="product-item">
                <div className="product-image">
                  {product.image_url && (
                    <Image
                      src={product.image_url}
                      alt={product.name}
                      width={100}
                      height={100}
                      style={{ objectFit: "contain" }}
                    />
                  )}
                </div>
                <div className="product-details">
                  <h3>{product.name}</h3>
                  <p>Kode: {product.code}</p>
                  <p>Kategori: {product.category_name}</p>
                  <p>
                    Harga:{" "}
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(product.selling_price)}
                  </p>
                  <p>
                    Jumlah: {parseFloat(product.quantity)} {product.unit_name}
                  </p>
                  <p>
                    Total:{" "}
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(product.total_selling_price)}
                  </p>

                  {product.type === "Persediaan" &&
                    product.serial_number_type === "UNIQUE" && (
                      <div className="serial-number-input">
                        <InputAutoComplete2
                          label="Serial Number"
                          inputName={`serial_number_${product.id}`}
                          formClass="full-width"
                          dataUrl="kiosk/admin/transaction/serial-number"
                          dataParams={{
                            product_id: product.id,
                          }}
                          displayTitle="serial_number"
                          displaySubtitle="warehouse_name"
                          required={true}
                          onChange={(item) => {
                            if (item) {
                              const updatedProducts = [...inputs.product_array];
                              updatedProducts[index] = {
                                ...product,
                                serial_number: item.serial_number,
                              };
                              this.setState({
                                inputs: {
                                  ...inputs,
                                  product_array: updatedProducts,
                                },
                              });
                            }
                          }}
                        />
                      </div>
                    )}
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  }
  renderFooter() {
    return (
      <>
        <button
          className="button info"
          onClick={() => this.onValidateListeners()}
        >
          <span>Verifikasi</span>
        </button>
        <button className="button" onClick={() => this.onCloseDialog()}>
          <span>Batal</span>
        </button>
      </>
    );
  }
}
